import { Router } from "express";
import { withRequestBody } from "../middlewares/misc";
import {
  createTutor,
  getAllTutors,
  deleteTutor,
  getOverviewInsights,
  getLessonsInsights,
  getSubscriptionsInsights,
} from "../controllers/tutor";
import { getProfileById } from "../hooks/profile";
import Tu<PERSON> from "../models/tutor";
import { getTutors } from "../controllers/tutorController_profiling";
import { isAuthenticated } from "../middlewares/auth";

const tutorRouter = Router();

const insightRouter = Router({ mergeParams: true });

insightRouter.use(isAuthenticated({ role: "tutor" }));
insightRouter.get("/overview", getOverviewInsights);
insightRouter.get("/lessons", getLessonsInsights);
insightRouter.get("/subscriptions", getSubscriptionsInsights);

tutorRouter.use("/:id/insight", insightRouter);

tutorRouter.route("/:id").get(getProfileById(Tutor)).delete(deleteTutor);

tutorRouter.route("/").post(withRequestBody(), createTutor).get(getAllTutors);

tutorRouter.get("/", getTutors);

export default tutorRouter;
