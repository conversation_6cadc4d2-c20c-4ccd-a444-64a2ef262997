import { Router } from 'express';
import {
  createCalendar,
  getTutorCalendars,
  updateCalendar,
  deleteCalendar,
  getMyCalendars
} from '../controllers/calendarController';
import { isAuthenticated } from '../middlewares/auth';
import { validateTutorOwnership, validateSubscriptionForViewing } from '../middlewares/subscriptionValidation';
import { withRequestBody } from '../middlewares/misc';

const calender_router = Router();

// Tutor calendar management routes
calender_router.post('/',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  createCalendar
);

calender_router.get('/my',
  isAuthenticated(),
  validateTutorOwnership,
  getMyCalendars
);

calender_router.put('/:id',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  updateCalendar
);

calender_router.delete('/:id',
  isAuthenticated(),
  validateTutorOwnership,
  deleteCalendar
);

// Public/student routes for viewing tutor calendars
calender_router.get('/tutor/:tutorId',
  isAuthenticated(),
  validateSubscriptionForViewing,  // This allows viewing but adds subscription info
  getTutorCalendars
);

export default calender_router;
