import express from "express";
import cors from "cors";
import helmet from "helmet";
import rateLimiter from "./middlewares/rateLimiter";
import mongoSanitize from "express-mongo-sanitize";
import xss from "xss-clean";
import errorHandler from "./middlewares/errorHandler";
import authRoutes from "./routes/auth";
import { CLIENT_ORIGIN, IS_PROD, SERVER_ORIGIN } from "./config/constants";
import { patchReadonlyExpressFields } from "./middlewares/patch";
import { handle404 } from "./middlewares/misc";
import classRoomRouter from "./routes/classroom";
import tutorRouter from "./routes/tutor";
import { handleStripeWebhook } from '../src/webhook/stripe_webhook'
import calendarRoutes from "./routes/calendarRoutes";
import eventRoutes from "./routes/eventRoutes";
import bookingRoutes from "./routes/bookingRoutes";
import schedulingRoutes from "./routes/schedulingRoutes";
import profileRouter from "./routes/profile";
import miscRouter from "./routes/misc";
import adminRouter from "./routes/adminRouter";

import Subscription from './routes/Subscriptions'

import { CORS_CONFIG } from "./config/misc";


const app = express();

// Middleware Setup

app.use(helmet()); // Protects headers
app.use(cors(CORS_CONFIG)); // Cross-origin
app.use(rateLimiter); // Protect from brute force
app.use(express.json({ limit: "10mb" })); // Body parser
app.use(patchReadonlyExpressFields(mongoSanitize())); // No $query injections
app.use(patchReadonlyExpressFields(xss())); // Clean inputs

// Routes

app.use("/api/profile", profileRouter);
app.use("/api/classroom", classRoomRouter);
app.use("/api/auth", authRoutes);
app.use("/api/tutor", tutorRouter);
app.use("/api/admin", adminRouter);

app.use("/api/calendars", calendarRoutes);
app.use("/api/events", eventRoutes);
app.use("/api/bookings", bookingRoutes);
app.use("/api/scheduling", schedulingRoutes);
app.use("/api/subscription", Subscription);

app.post('/webhook/stripe', 
  express.raw({ type: 'application/json' }), 
  handleStripeWebhook
);

app.use("/api", miscRouter);

app.use(handle404);

// Error Handling

app.use(errorHandler);

export default app;


