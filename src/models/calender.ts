import { Schema, model, Document, Types } from 'mongoose';

export interface ICalendar extends Document {
  tutorId: Types.ObjectId; // Must be a tutor
  name: string;
  description?: string;
  color?: string;
  isShared?: boolean; // whether this calendar is visible to learners or public
  timezone?: string; // <PERSON><PERSON>'s timezone for proper scheduling
  isActive?: boolean; // Whether the calendar is currently active
  bookingSettings?: {
    autoAcceptBookings?: boolean;
    advanceBookingDays?: number; // How many days in advance can students book
    minBookingNotice?: number; // Minimum hours notice required for booking
    maxBookingsPerDay?: number; // Maximum bookings allowed per day
  };
  createdAt: Date;
  updatedAt: Date;
}

const calendarSchema = new Schema<ICalendar>({
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  name: { type: String, required: true },
  description: String,
  color: { type: String, default: '#3B82F6' }, // Blue color
  isShared: { type: Boolean, default: true }, // Tutors want learners to see their calendar by default
  timezone: { type: String, default: 'UTC' },
  isActive: { type: Boolean, default: true },
  bookingSettings: {
    autoAcceptBookings: { type: Boolean, default: false },
    advanceBookingDays: { type: Number, default: 30 },
    minBookingNotice: { type: Number, default: 24 }, // 24 hours
    maxBookingsPerDay: { type: Number, default: 8 }
  },
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Add indexes for better query performance
calendarSchema.index({ tutorId: 1 });
calendarSchema.index({ tutorId: 1, isActive: 1 });
calendarSchema.index({ isShared: 1, isActive: 1 });

calendarSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Instance methods
calendarSchema.methods.canAcceptBookings = function(): boolean {
  return this.isActive && this.isShared;
};

calendarSchema.methods.getBookingWindow = function(): { start: Date, end: Date } {
  const now = new Date();
  const start = new Date(now.getTime() + (this.bookingSettings?.minBookingNotice || 24) * 60 * 60 * 1000);
  const end = new Date(now.getTime() + (this.bookingSettings?.advanceBookingDays || 30) * 24 * 60 * 60 * 1000);
  return { start, end };
};

export const Calendar = model<ICalendar>('Calendar', calendarSchema);
