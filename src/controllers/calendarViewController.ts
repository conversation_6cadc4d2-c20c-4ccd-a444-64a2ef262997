import { Response } from 'express';
import { Types } from 'mongoose';
import { Calendar } from '../models/calender';
import { Event } from '../models/Event';
import { Booking } from '../models/Booking';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

export enum CalendarViewType {
  MONTH = 'month',
  WEEK = 'week',
  DAY = 'day',
  AGENDA = 'agenda'
}

interface CalendarViewOptions {
  viewType: CalendarViewType;
  startDate: Date;
  endDate: Date;
  timezone: string;
  includeAvailable: boolean;
  includeBooked: boolean;
  calendarIds?: string[];
}

/**
 * Get calendar view with events formatted for different view types
 */
export const getCalendarView = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const {
      viewType = CalendarViewType.MONTH,
      startDate,
      endDate,
      timezone = 'UTC',
      includeAvailable = true,
      includeBooked = true,
      calendarIds
    } = req.query;

    // Validate and parse dates
    const start = startDate ? new Date(startDate as string) : getDefaultStartDate(viewType as CalendarViewType);
    const end = endDate ? new Date(endDate as string) : getDefaultEndDate(start, viewType as CalendarViewType);

    // Build calendar query based on user role
    let calendarQuery: any = {};
    if (req.user.role === 'tutor') {
      calendarQuery.tutorId = req.user._id;
    } else {
      // For students, only show shared and active calendars
      calendarQuery.isShared = true;
      calendarQuery.isActive = true;
    }

    // Filter by specific calendars if provided
    if (calendarIds) {
      const ids = Array.isArray(calendarIds) ? calendarIds : [calendarIds];
      calendarQuery._id = { $in: ids.map(id => new Types.ObjectId(id as string)) };
    }

    // Get calendars
    const calendars = await Calendar.find(calendarQuery)
      .populate('tutorId', 'firstname lastname email avatar');

    if (calendars.length === 0) {
      res.json({
        success: true,
        data: {
          calendars: [],
          events: [],
          viewInfo: {
            viewType,
            startDate: start,
            endDate: end,
            timezone
          }
        }
      });
      return;
    }

    // Build event query
    const eventQuery: any = {
      calendarId: { $in: calendars.map(c => c._id) },
      startDateTime: { $gte: start, $lte: end }
    };

    // Filter by event status
    const statusFilter = [];
    if (includeAvailable === 'true') statusFilter.push('available');
    if (includeBooked === 'true') statusFilter.push('booked');
    
    if (statusFilter.length > 0) {
      eventQuery.status = { $in: statusFilter };
    }

    // Get events
    const events = await Event.find(eventQuery)
      .populate('calendarId', 'name color')
      .sort({ startDateTime: 1 });

    // Get bookings for booked events (if user is authorized)
    let bookingsMap = new Map();
    if (req.user.role === 'tutor' || req.user.role === 'student') {
      const bookedEventIds = events
        .filter(event => event.status === 'booked')
        .map(event => event._id);

      if (bookedEventIds.length > 0) {
        let bookingQuery: any = { eventId: { $in: bookedEventIds } };
        
        // Filter bookings based on user role
        if (req.user.role === 'student') {
          bookingQuery.studentId = req.user._id;
        } else if (req.user.role === 'tutor') {
          bookingQuery.tutorId = req.user._id;
        }

        const bookings = await Booking.find(bookingQuery)
          .populate('studentId', 'firstname lastname email avatar')
          .populate('tutorId', 'firstname lastname email avatar');

        bookings.forEach(booking => {
          bookingsMap.set(booking.eventId.toString(), booking);
        });
      }
    }

    // Format events for calendar view
    const formattedEvents = events.map(event => {
      const booking = bookingsMap.get((event._id as any).toString());
      const calendar = event.calendarId as any;

      return {
        id: event._id,
        title: event.title,
        start: event.startDateTime,
        end: event.endDateTime,
        allDay: event.allDay,
        status: event.status,
        location: event.location,
        description: event.description,
        calendar: {
          id: calendar._id,
          name: calendar.name,
          color: calendar.color
        },
        booking: booking ? {
          id: booking._id,
          status: booking.status,
          student: booking.studentId,
          tutor: booking.tutorId,
          notes: booking.bookingNotes
        } : null,
        canBook: event.status === 'available' && 
                event.startDateTime > new Date() &&
                (event.bookingInfo?.currentBookings || 0) < (event.bookingInfo?.maxStudents || 1),
        duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
        price: event.bookingInfo?.price,
        maxStudents: event.bookingInfo?.maxStudents || 1,
        currentBookings: event.bookingInfo?.currentBookings || 0
      };
    });

    // Generate view-specific data
    const viewData = generateViewData(formattedEvents, start, end, viewType as CalendarViewType);

    res.json({
      success: true,
      data: {
        calendars: calendars.map(cal => ({
          id: cal._id,
          name: cal.name,
          color: cal.color,
          tutor: cal.tutorId,
          isShared: cal.isShared,
          isActive: cal.isActive
        })),
        events: formattedEvents,
        viewData,
        viewInfo: {
          viewType,
          startDate: start,
          endDate: end,
          timezone,
          totalEvents: formattedEvents.length,
          availableSlots: formattedEvents.filter(e => e.status === 'available').length,
          bookedSlots: formattedEvents.filter(e => e.status === 'booked').length
        }
      }
    });

  } catch (error) {
    console.error('Error fetching calendar view:', error);
    createErrorResponse(res, 'Failed to fetch calendar view', 500);
  }
};

/**
 * Get default start date based on view type
 */
function getDefaultStartDate(viewType: CalendarViewType): Date {
  const now = new Date();
  
  switch (viewType) {
    case CalendarViewType.MONTH:
      return new Date(now.getFullYear(), now.getMonth(), 1);
    case CalendarViewType.WEEK:
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      startOfWeek.setHours(0, 0, 0, 0);
      return startOfWeek;
    case CalendarViewType.DAY:
      const startOfDay = new Date(now);
      startOfDay.setHours(0, 0, 0, 0);
      return startOfDay;
    case CalendarViewType.AGENDA:
      return new Date(now);
    default:
      return new Date(now);
  }
}

/**
 * Get default end date based on view type and start date
 */
function getDefaultEndDate(startDate: Date, viewType: CalendarViewType): Date {
  const end = new Date(startDate);
  
  switch (viewType) {
    case CalendarViewType.MONTH:
      end.setMonth(end.getMonth() + 1);
      end.setDate(0); // Last day of the month
      end.setHours(23, 59, 59, 999);
      return end;
    case CalendarViewType.WEEK:
      end.setDate(end.getDate() + 6);
      end.setHours(23, 59, 59, 999);
      return end;
    case CalendarViewType.DAY:
      end.setHours(23, 59, 59, 999);
      return end;
    case CalendarViewType.AGENDA:
      end.setDate(end.getDate() + 30); // Next 30 days
      return end;
    default:
      return new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
  }
}

/**
 * Generate view-specific data structure
 */
function generateViewData(events: any[], startDate: Date, endDate: Date, viewType: CalendarViewType): any {
  switch (viewType) {
    case CalendarViewType.MONTH:
      return generateMonthViewData(events, startDate, endDate);
    case CalendarViewType.WEEK:
      return generateWeekViewData(events, startDate, endDate);
    case CalendarViewType.DAY:
      return generateDayViewData(events, startDate);
    case CalendarViewType.AGENDA:
      return generateAgendaViewData(events);
    default:
      return { events };
  }
}

/**
 * Generate month view data with days grid
 */
function generateMonthViewData(events: any[], startDate: Date, endDate: Date): any {
  const weeks = [];
  const current = new Date(startDate);
  
  // Start from the beginning of the week containing the first day of the month
  current.setDate(current.getDate() - current.getDay());
  
  while (current <= endDate) {
    const week = [];
    for (let i = 0; i < 7; i++) {
      const dayEvents = events.filter(event => {
        const eventDate = new Date(event.start);
        return eventDate.toDateString() === current.toDateString();
      });
      
      week.push({
        date: new Date(current),
        events: dayEvents,
        isCurrentMonth: current.getMonth() === startDate.getMonth(),
        isToday: current.toDateString() === new Date().toDateString()
      });
      
      current.setDate(current.getDate() + 1);
    }
    weeks.push(week);
  }
  
  return { weeks };
}

/**
 * Generate week view data with time slots
 */
function generateWeekViewData(events: any[], startDate: Date, endDate: Date): any {
  const days = [];
  const current = new Date(startDate);
  
  for (let i = 0; i < 7; i++) {
    const dayEvents = events.filter(event => {
      const eventDate = new Date(event.start);
      return eventDate.toDateString() === current.toDateString();
    });
    
    days.push({
      date: new Date(current),
      events: dayEvents.sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()),
      isToday: current.toDateString() === new Date().toDateString()
    });
    
    current.setDate(current.getDate() + 1);
  }
  
  return { days };
}

/**
 * Generate day view data with hourly time slots
 */
function generateDayViewData(events: any[], date: Date): any {
  const hours = [];
  
  for (let hour = 0; hour < 24; hour++) {
    const hourStart = new Date(date);
    hourStart.setHours(hour, 0, 0, 0);
    
    const hourEnd = new Date(date);
    hourEnd.setHours(hour, 59, 59, 999);
    
    const hourEvents = events.filter(event => {
      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);
      
      return (eventStart >= hourStart && eventStart <= hourEnd) ||
             (eventEnd >= hourStart && eventEnd <= hourEnd) ||
             (eventStart <= hourStart && eventEnd >= hourEnd);
    });
    
    hours.push({
      hour,
      time: hourStart.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      events: hourEvents
    });
  }
  
  return { date, hours };
}

/**
 * Generate agenda view data grouped by date
 */
function generateAgendaViewData(events: any[]): any {
  const groupedEvents = events.reduce((groups, event) => {
    const date = new Date(event.start).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(event);
    return groups;
  }, {} as Record<string, any[]>);
  
  const agenda = Object.keys(groupedEvents)
    .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
    .map(dateStr => ({
      date: new Date(dateStr),
      events: groupedEvents[dateStr].sort((a: any, b: any) => new Date(a.start).getTime() - new Date(b.start).getTime())
    }));
  
  return { agenda };
}
